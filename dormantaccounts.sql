with 

cte_activeMembers as (
 select distinct MemberSuppID
 from ECUProd.dbo.MainMemberSuppBaseByMostRecentActivityDate mbr
 left join ARCUSYM000.dbo.dimDate dd on mbr.ProcessDate = dd.DateKey
 where ProcessDate = (select datekey from ARCUSYM000.arcu.vwProcessDate)
 and MembershipCloseDate is null
 and MembershipChargedOffDate is null
 and DATEDIFF(month, AccountActivityDate, dd.FullDate) < 19
),

--cte_isInactiveOnActiveJoint as (
-- select NameSSN
--    ,nm.AccountNumber
--    ,acct.AccountActivityDate
-- from ARCUSYM000.arcu.vwARCUName nm
-- left join ARCUSYM000.arcu.ARCUAccountDetailed acct on nm.ProcessDate = acct.ProcessDate and nm.AccountNumber = acct.AccountNumber
-- where nm.ProcessDate = (select datekey from ARCUSYM000.arcu.vwProcessDate)
-- and acct.AccountCloseDate is null
-- and acct.CountLoansChargeOff = 0
-- and acct.CountShareChargeOff = 0
-- and NameType = 1
-- and NameSSN is not null
-- and NameSSN != '*********'
-- and DATEDIFF(month, AccountActivityDate, acct.FullDate) < 19 
--),

cte_custodialExempt as (
 select AccountNumber
 from ARCUSYM000.arcu.ARCUAccountDetailed ad
 where ad.ProcessDate = (select datekey from ARCUSYM000.arcu.vwProcessDate)
 and AccountCloseDate is null
 and CountLoansChargeOff = 0
 and CountShareChargeOff = 0
 and AccountType = 6
 and SumOpenShareBalance >= 100
),

cte_shareExemptionData as (
 select distinct MemberSuppID
 from (
  select mbr.MemberSuppID
     ,sd.AccountNumber
  from ARCUSYM000.arcu.vwARCUShareDetailed sd
  left join ECUProd.dbo.MainMemberAccountsBase mbr on sd.ProcessDate = mbr.ProcessDate and sd.AccountNumber = mbr.AccountNumber
  where sd.ProcessDate = (select datekey from ARCUSYM000.arcu.vwProcessDate)
  and ShareCloseDate is null
  --and ShareChargeOffDate is null
  and (ShareType = 13 or ShareCode = 2 or ShareIRSCode != 0 ) --HSA, CDs and IRAs
 ) a
),

cte_loanExemptData as (
 select distinct MemberSuppID
 from (
  select mbr.MemberSuppID
     ,ld.AccountNumber
  from ARCUSYM000.arcu.vwARCULoanDetailed ld
  left join ECUProd.dbo.MainMemberAccountsBase mbr on ld.ProcessDate = mbr.ProcessDate and ld.AccountNumber = mbr.AccountNumber
  where ld.ProcessDate = (select datekey from ARCUSYM000.arcu.vwProcessDate)
  and LoanCloseDate is null
  --and LoanChargeoffDate is null
 ) a
),

cte_externalLoanExemptData as (
 select distinct MemberSuppID
 from (
  select mbr.MemberSuppID
      ,el.PARENTACCOUNT
  from ARCUSYM000.dbo.EXTERNALLOAN el
  left join ECUProd.dbo.MainMemberAccountsBase mbr on el.ProcessDate = mbr.ProcessDate and el.PARENTACCOUNT = mbr.AccountNumber
  where el.ProcessDate = (select datekey from ARCUSYM000.arcu.vwProcessDate)
  and CLOSEDATE is null
 ) a
),

cte_CUBGExemptData as (
 select distinct MemberSuppID
 from (
  select mbr.MemberSuppID
      ,tr.PARENTACCOUNT
  from ARCUSYM000.dbo.TRACKING tr
  left join ECUProd.dbo.MainMemberAccountsBase mbr on tr.ProcessDate = mbr.ProcessDate and tr.PARENTACCOUNT = mbr.AccountNumber
  where tr.ProcessDate = (select datekey from ARCUSYM000.arcu.vwProcessDate)
  and EXPIREDATE is null
  and type = 49
 ) a
),

cte_LendKey as (
 select distinct MemberSuppID
 from ECUProd.dbo.vwMemberAccount_LendKeyWarningALL
),

cte_dormantFee as (
 select distinct PARENTACCOUNT
 from ARCUSYM000.dbo.SAVINGSTRANSACTION
 where sourcecode = 'F'
 and DESCRIPTION = 'Dormant Fee'
 and POSTDATE = (select eomonth(dateadd(month, -1, processdate)) from ARCUSYM000.arcu.vwProcessDate)
)

select acct.FullDate
      ,mbr.LastFirstName
   ,sum(acct.SumOpenShareBalance) as sumAllInactiveAccountBalance
   ,STRING_AGG(mbr.AccountNumber, ', ') as inactiveAccountList
   ,STRING_AGG(mbr.AccountType, ', ') as inactiveAccountTypeList
   ,max(acct.AccountActivityDate) as lastActivityDate

from ECUProd.dbo.MainMemberAccountsBase mbr
left join cte_activeMembers ambr on mbr.MemberSuppID = ambr.MemberSuppID
left join cte_shareExemptionData sed    on mbr.MemberSuppID = sed.MemberSuppID
left join cte_loanExemptData led  on mbr.MemberSuppID = led.MemberSuppID
left join cte_externalLoanExemptData el on mbr.MemberSuppID = el.MemberSuppID
left join cte_CUBGExemptData ced  on mbr.MemberSuppID = ced.MemberSuppID
left join cte_LendKey lk    on mbr.MemberSuppID = lk.MemberSuppID
left join cte_dormantFee df    on mbr.AccountNumber = df.PARENTACCOUNT
--left join cte_isInactiveOnActiveJoint j on mbr.SSN = j.NameSSN
left join ARCUSYM000.arcu.ARCUAccountDetailed acct on mbr.ProcessDate = acct.ProcessDate and mbr.AccountNumber = acct.AccountNumber
left join cte_custodialExempt cst       on acct.AccountNumber = cst.AccountNumber
where mbr.MembershipCloseDate is null
and mbr.MembershipChargedOffDate is null
and mbr.ProcessDate = (select datekey from ARCUSYM000.arcu.vwProcessDate)
and ambr.MemberSuppID is null
and sed.MemberSuppID is null
and el.MemberSuppID is null
and ced.MemberSuppID is null
and lk.MemberSuppID is null
and led.MemberSuppID is null
and mbr.AccountCloseDate is null
and df.PARENTACCOUNT is null
--and j.NameSSN is null
and cst.AccountNumber is null
and acct.SumOpenShareBalance < 5000
and acct.CountLoansChargeOff = 0
and acct.CountShareChargeOff = 0
--and mbr.AccountType not in (6, 13, 14, 15, 30, 31, 32, 33, 34, 35)
group by acct.FullDate, mbr.LastFirstName