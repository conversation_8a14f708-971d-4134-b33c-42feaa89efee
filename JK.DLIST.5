[JK.DLIST.5
 changed time frame from 3 years to 18 months - j<PERSON> knapton, 6/29/16 
 
 exclude share types 1 and 12
 If account balance is over $5,000, the $5 Monthly Dormant Fee is waived and exception report created
 exclude if primary or joint is active on any other accounts within the last 18 months
 
 Al Zehrung 04/30/21  sw103174
 
 When setting warning 16, also remove WARNING 48 and WARNING 23 if they exist.
 
 AL ZEHRUNG - 10/22/2021 IS-287
 
 Exclude External Accounts from getting warning 16 placed on them
 
 Al Zehrung 08/03/21  IS-2708 ]

TARGET=ACCOUNT

DEFINE
 #INCLUDE "RD.OUTPUT.DEF"
 ACCTCOUNT=NUMBER
 COFLG=NUMBER
 OTHERACCTFLG=NUMBER
 SLFLG=NUMBER
 AGGBALFLG=NUMBER
 U21FLG=NUMBER
 AGGBALHEADER=NUMBER
 COMMA=CHARACTER(1)
 CR=CHARACTER(1)
 LF=CHARACTER(1)
 NAME1=CHARACTER
 NAME2=CHARACTER
 STREET1=CHARACTER
 CITY1=CHARACTER
 STATE1=CHARACTER
 ZIP1=CHARACTER
 LIMITDATE=DATE
 MASKACCT=CHARACTER
 X=NUMBER
 SSNCOUNT=NUMBER
 ERRORTEXT=CHARACTER
 TEMPSSN=CHARACTER(9) ARRAY(99)
 AGGSHAREBAL=MONEY
 TEMPACCT=CHARACTER
 THISACCT=CHARACTER
 TRUE=1
 FALSE=0
 OC1=NUMBER
 OC2=NUMBER
 WARNDATE=CHARACTER
 EXLNFLG=NUMBER
END

SETUP
 ACCTCOUNT=0
 COMMA=CTRLCHR(124)
 CR=CTRLCHR(13)
 LF=CTRLCHR(10)
 LIMITDATE=DATEOFFSET(SYSTEMDATE,-19,1)
 TEMPACCT=""
 AGGBALHEADER=FALSE
 EXLNFLG=FALSE
 WARNDATE="12/31/2050"
 
 OUTPUTOPEN(OUTPUTDEVREPORT,0,"Agg Bal over $5K","",OC1,ERRORTEXT)
 IF ERRORTEXT<>"" THEN CALL ERRORHANDLER
 
 OUTPUTOPEN(OUTPUTDEVREPORT,0,"Set Warning 16 FM","",OC2,ERRORTEXT)
 IF ERRORTEXT<>"" THEN CALL ERRORHANDLER
END

SELECT
 ACCOUNT:CLOSEDATE='--/--/--' AND
(ACCOUNT:ACTIVITYDATE<=DATEOFFSET(SYSTEMDATE,-1.5*12,0) AND
 ACCOUNT:ACTIVITYDATE>LIMITDATE) AND
(ACCOUNT:CORRESPONDDATE='--/--/--' OR
(ACCOUNT:CORRESPONDDATE<=DATEOFFSET(SYSTEMDATE,-1.5*12,0) AND
 ACCOUNT:CORRESPONDDATE>LIMITDATE) OR
 ACCOUNT:CORRESPONDDATE<ACCOUNT:ACTIVITYDATE) AND
 ACCOUNT:TYPE<>6 AND
 NAME:BIRTHDATE<DATEOFFSET(SYSTEMDATE,-21*12,0) AND
 NOT ANYWARNING(ACCOUNT,04) AND
 NOT ANYWARNING(ACCOUNT,06) AND
 NOT ANYWARNING(ACCOUNT,11) AND
 NOT ANYWARNING(ACCOUNT,21) AND
 NOT ANYWARNING(ACCOUNT,27) AND
 NOT ANYWARNING(ACCOUNT,31) AND
 NOT ANYWARNING(ACCOUNT,34) AND
 NOT ANYWARNING(ACCOUNT,35) AND
 NOT ANYWARNING(ACCOUNT,46) AND
 NOT ANYWARNING(ACCOUNT,57) AND
 NOT ANYWARNING(ACCOUNT,60) AND
 NOT ANYWARNING(ACCOUNT,62) AND
 NOT ANYWARNING(ACCOUNT,63) AND
 NOT ANYWARNING(ACCOUNT,74) AND
 NOT ANYWARNING(ACCOUNT,82) AND
 NOT ANYWARNING(ACCOUNT,83) AND
 NOT ANYWARNING(ACCOUNT,84) AND
 NOT ANYWARNING(ACCOUNT,85) AND
 NOT ANYWARNING(ACCOUNT,86) AND
 NOT ANYWARNING(SHARE,78) AND
 NOT ANYWARNING(ACCOUNT,96) AND
 NOT ANYWARNING(ACCOUNT,81) AND
 NOT ANYWARNING(ACCOUNT,99) AND
 NOT ANY SHARE WITH (SHARE:TYPE=1 OR
                     SHARE:TYPE=7 OR
                     SHARE:TYPE=12 OR
                     SHARE:TYPE=13 OR
                     SHARE:TYPE>=40) AND
 NOT ANY LOAN WITH (LOAN:BALANCE>$0.00 AND
                    LOAN:CHARGEOFFDATE<>'--/--/--' OR
                    LOAN:TYPE=21)
END

PRINT TITLE="Dormant Document # 5" DATAFILE ASCII
    RECORDSIZE = 187 BLOCKSIZE = 187
 HEADERS END 
 COFLG=FALSE 
 OTHERACCTFLG=FALSE
 SLFLG=FALSE
 AGGBALFLG=FALSE
 U21FLG=FALSE
 NAME2=""
 MASKACCT="XXXXXX"
 MASKACCT=MASKACCT+SEGMENT(ACCOUNT:NUMBER,7,10)
 THISACCT=ACCOUNT:NUMBER

 X=0
 FOR EACH NAME WITH (NAME:TYPE=0 OR NAME:TYPE=1) AND NAME:SSN<>""
  DO
   TEMPSSN(X)=NAME:SSN
   X=X+1
  END
  SSNCOUNT=X-1

  FOR X=0 TO SSNCOUNT
   DO
    FOR ACCOUNT WITH SSN TEMPSSN(X)
     DO
      IF ACCOUNT:ACTIVITYDATE>=DATEOFFSET(SYSTEMDATE,-1.5*12,0) THEN
       DO
        FOR EACH NAME WITH ((NAME:TYPE=0 OR NAME:TYPE=1) AND NAME:SSN=TEMPSSN(X))
         DO
          TEMPACCT=ACCOUNT:NUMBER
          IF TEMPACCT<>THISACCT THEN
           DO
            OTHERACCTFLG=TRUE
           END
         END
       END
     END
    UNTIL OTHERACCTFLG=TRUE
   END

   FOR ACCOUNT THISACCT 
    DO
     AGGSHAREBAL=$0.00
     FOR EACH SHARE WITH (SHARE:CLOSEDATE='--/--/--')
      DO
       AGGSHAREBAL=AGGSHAREBAL+SHARE:BALANCE
      END
      
      FOR EACH EXTERNALLOAN
       DO   
        IF EXTERNALLOAN:BALANCE>$0.00 THEN EXLNFLG=TRUE   
       END

      IF AGGSHAREBAL>=$5,000.00 THEN
       DO
        AGGBALFLG=TRUE
        CALL PRINTSHAREBALEXCEPTION
       END

      IF (OTHERACCTFLG=FALSE AND AGGBALFLG=FALSE) THEN
       DO
        IF NOT ANYWARNING(ACCOUNT,13) THEN
         DO
          ACCTCOUNT=ACCTCOUNT+1
          CALL PRINTDATA
         END
         
         IF NOT ANYWARNING(ACCOUNT,16) THEN
          DO
           CALL UPDATEWARN
          END
       END  
    END
END

PROCEDURE PRINTDATA
 FOR EACH NAME WITH (NAME:TYPE=0)
  DO
   NAME1=NAME:LONGNAME
   STREET1=NAME:STREET
   CITY1=NAME:CITY
   STATE1=NAME:STATE
   ZIP1=NAME:ZIPCODE
  END

[OVERWRITE THESE VALUES WITH MAILING NAME TYPE, IF MAILING NAME TYPE EXISTS]
 FOR EACH NAME WITH (NAME:TYPE=2)
  DO
   NAME1=NAME:LONGNAME
   STREET1=NAME:STREET
   CITY1=NAME:CITY
   STATE1=NAME:STATE
   ZIP1=NAME:ZIPCODE
  END

 FOR EACH NAME WITH (NAME:TYPE=1)
  DO
   NAME2=NAME:LONGNAME
  END

  DATASIZE=40 NAME1
  DATASIZE=1  COMMA
  DATASIZE=40 NAME2
  DATASIZE=1  COMMA
  DATASIZE=40 STREET1
  DATASIZE=1  COMMA
  DATASIZE=40 CITY1
  DATASIZE=1  COMMA
  DATASIZE=10 STATE1
  DATASIZE=1  COMMA
  DATASIZE=10 ZIP1
  DATASIZE=1  COMMA
  DATASIZE=10 MASKACCT
  DATASIZE=1  COMMA
  DATASIZE=10 ACCOUNT:NUMBER
  DATASIZE=1  COMMA
  IF ANYWARNING(ACCOUNT,17) THEN STATE1="17"
  ELSE STATE1=""
  DATASIZE=2  STATE1
  DATASIZE=1  CR
  DATASIZE=1  LF
END

PROCEDURE PRINTSHAREBALEXCEPTION
 OUTPUTSWITCH(OC1,ERRORTEXT)
 IF AGGBALHEADER=FALSE THEN
  DO
   HEADERS END
   COL=001 "Acct. Num."
   COL=012 "Agg. Bal."
   COL=023 "Pri. Name"
   COL=040 "Street"
   COL=070 "City"
   COL=085 "St"
   COL=088 "Zip"
   COL=099 "Email"
   NEWLINE   
   AGGBALHEADER=TRUE
  END
  COL=001 ACCOUNT:NUMBER
  COL=012 LEFT AGGSHAREBAL
  COL=023 NAME:SHORTNAME
  COL=040 NAME:STREET
  COL=070 NAME:CITY
  COL=085 NAME:STATE
  COL=088 NAME:ZIPCODE
  COL=099 SEGMENT(NAME:EMAIL,1,33)
  NEWLINE
  OUTPUTSWITCH(OUTPUTCHANNELDEFAULT,ERRORTEXT)
END

PROCEDURE UPDATEWARN
 OUTPUTSWITCH(OC2,ERRORTEXT) 
 PRINT "ACCOUNT "+ACCOUNT:NUMBER+" MODIFY"
 NEWLINE
 IF EXLNFLG=FALSE THEN
  DO
   PRINT " CHANGE SETWARNING 16 "+WARNDATE
   NEWLINE
  END 
  IF ANYWARNING(ACCOUNT,48) THEN 
   DO   
    PRINT " CHANGE CLEARWARNING 48 "
    NEWLINE
   END  
  IF ANYWARNING(ACCOUNT,23) THEN 
   DO
    PRINT " CHANGE CLEARWARNING 23 "
    NEWLINE   
   END   
 OUTPUTSWITCH(OUTPUTCHANNELDEFAULT,ERRORTEXT)
END

PROCEDURE ERRORHANDLER
 PRINT ERRORTEXT
 NEWLINE
 TERMINATE
END
